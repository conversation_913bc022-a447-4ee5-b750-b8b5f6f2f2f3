# Configuração centralizada para todos os scripts de análise financeira
# Este arquivo contém todos os parâmetros utilizados nos scripts da pasta src/

# ============================================================================
# CONFIGURAÇÕES GERAIS
# ============================================================================
general:
  # Configurações de matplotlib
  matplotlib_backend: 'Agg'  # Backend para salvar sem display
  matplotlib_style: 'default'
  
  # Configurações de warnings
  suppress_warnings: true
  
  # Configurações de random seed para reprodutibilidade
  random_seed: 42

# ============================================================================
# CONFIGURAÇÕES DE DADOS
# ============================================================================
data:
  # Arquivos de entrada
  files:
    carteira: 'carteira.csv'
    acoes_listadas_b3: 'acoes-listadas-b3.csv'
    acoes_diversificacao: 'results/csv/correlation_data/acoes_diversificacao.csv'
  
  # Períodos de dados históricos
  periods:
    # Para análises gerais
    default_period: '18mo'  # 18 meses
    correlation_period: '1y' # 1 ano para correlação
    
  # Datas específicas
  dates:
    portfolio_start_date: '2025-06-26'  # Data de início da carteira
    tims3_correction_limit: '2025-07-02'  # Data limite para correção TIMS3
  
  # Configurações de correção de dados
  corrections:
    tims3_division_factor: 100  # Fator de divisão para correção TIMS3
    replace_zero_with_previous: true  # Substituir zeros com valor anterior

# ============================================================================
# CONFIGURAÇÕES DE MÉDIAS MÓVEIS
# ============================================================================
moving_averages:
  # Janelas das médias móveis (em dias)
  windows:
    mm10: 10
    mm25: 25
    mm50: 50
    mm100: 100
    mm200: 200
  
  # Configurações específicas
  use_ohlc_average: true  # Usar média OHLC nos dias anteriores, OHL no dia atual (trading intraday)
  exponential_smoothing: true  # Usar suavização exponencial

# ============================================================================
# CONFIGURAÇÕES DO FILTRO BUTTERWORTH
# ============================================================================
butterworth:
  # Ordem do filtro
  filter_order: 2
  
  # Frequências de corte para cada média móvel
  cutoff_frequencies:
    mm10: 0.18   # Filtro mais rápido
    mm25: 0.05   # Filtro intermediário
    mm100: 0.02  # Filtro lento
    mm200: 0.009 # Filtro mais lento
  
  # Tipo de filtro
  filter_type: 'low'  # Passa-baixa
  analog: false


# ============================================================================
# CONFIGURAÇÕES DE CORRELAÇÃO
# ============================================================================
correlation:
  # Número de ações para análise de diversificação
  diversification_stocks: 40
  
  # Threshold de correlação para eliminação
  high_correlation_threshold: 0.9
  
  # Priorizar ações que pagam dividendos
  prioritize_dividend_stocks: true
  
  # Configurações do mapa de calor
  heatmap:
    figsize: [12, 10]
    colormap: 'coolwarm'
    center: 0

# ============================================================================
# CONFIGURAÇÕES DE VISUALIZAÇÃO
# ============================================================================
visualization:
  # Configurações gerais de gráficos
  figure_size: [16, 12]
  dpi: 100
  
  # Configurações de cores
  colors:
    price: '#1f77b4'      # Azul para preços
    mm10: 'blue'          # Azul para MM10
    mm25: 'green'         # Verde para MM25
    mm50: 'orange'        # Laranja para MM50
    mm100: 'purple'       # Roxo para MM100
    mm200: 'red'          # Vermelho para MM200
    volume: 'gray'        # Cinza para volume
    spread: 'brown'       # Marrom para spread
    prediction: 'orange'  # Laranja para previsões
    trend_up: 'green'     # Verde para tendência alta
    trend_down: 'red'     # Vermelho para tendência baixa
    trend_neutral: 'gray' # Cinza para tendência neutra
  
  # Configurações de linha
  line_widths:
    price: 2.5
    mm10: 5
    mm25: 2
    mm50: 2
    mm100: 2
    mm200: 2
    prediction: 3
  
  # Configurações de transparência
  alpha_values:
    main_lines: 0.8
    fill_areas: 0.3
    volume_bars: 0.6
  
  # Posição da legenda
  legend_position: 'upper left'  # Evitar sobreposição com dados

# ============================================================================
# CONFIGURAÇÕES DE ESTRATÉGIA DE TRADING
# ============================================================================
trading_strategy:
  # Configurações de sinais
  signals:
    buy_signal: 'cross_above'   # Cruzamento de baixo para cima
    sell_signal: 'cross_below'  # Cruzamento de cima para baixo
    
  # Linha de referência para posição inicial
  initial_position_reference: 'mm25'  # Usar apenas MM25
  
  # Configurações de execução
  no_action_last_day: true  # Não executar ações no último dia
  only_sell_owned_stocks: true  # Vender apenas ações em carteira
  
  # Configurações de contabilidade
  subtract_sales_from_invested: true  # Subtrair vendas do valor investido
  track_both_invested_and_current: true  # Rastrear investido e atual

# ============================================================================
# CONFIGURAÇÕES DE SAÍDA
# ============================================================================
output:
  # Diretórios de saída
  directories:
    results: 'results'
    figures: 'results/figures'
    csv: 'results/csv'
    individual_analysis: 'results/csv/individual_analysis'
    correlation_data: 'results/csv/correlation_data'
    kalman_analysis: 'results/csv/kalman_analysis'
    lstm_analysis: 'results/csv/lstm_analysis'
    butterworth_analysis: 'results/csv/butterworth_analysis'
    portfolio_analysis: 'results/csv/portfolio_analysis'
  
  # Configurações de arquivo
  file_formats:
    figures: 'png'
    data: 'csv'
  
  # Limpeza de arquivos antigos
  clean_old_files: true  # Limpar figuras e CSVs antigos antes de gerar novos

# ============================================================================
# CONFIGURAÇÕES DE API
# ============================================================================
api:
  # Chaves de API (manter em variáveis de ambiente em produção)
  keys:
    alpha_vantage: 'ELGIUR9A6KBUAP47'
    twelve_data: 'aadf74ae14d74fd7ac40a433dad50f51'
  
  # Configurações de timeout e retry
  timeout: 30  # segundos
  max_retries: 3
  retry_delay: 1  # segundos

# ============================================================================
# CONFIGURAÇÕES DE SPREAD
# ============================================================================
spread:
  # Configurações do edge_rolling
  window: 20  # Janela para cálculo do spread
  sign: true  # Manter sinal do spread
  
  # Conversão para percentual
  convert_to_percentage: true
  percentage_multiplier: 100
  
  # Fallback para volatilidade quando spread não disponível
  use_volatility_fallback: true
  volatility_window: 20

# ============================================================================
# CONFIGURAÇÕES DO XGBOOST
# ============================================================================
xgboost:
  # Período de dados para treinamento
  data_period: '15y'  # 15 anos de dados históricos

  # Configurações de cache histórico
  cache:
    use_cache: true  # Usar cache para acelerar downloads
    use_unified: true  # Usar cache unificado (MAIS EFICIENTE - todas as ações em um arquivo)
    use_optimized: true  # Usar cache Parquet otimizado (requer pyarrow)
    force_download: false  # Forçar download completo (ignorar cache)
    force_training: true  # Forçar treinamento dos modelos (ignorar cache de modelos)
    max_days_outdated: 3  # Máximo de dias desatualizados antes de forçar atualização

  # Definição dos sinais (NOVA LÓGICA: comparação com dia anterior)
  signal_horizon: 1  # Não usado mais - sinais agora baseados na comparação com dia anterior

  # Features
  features:
    # Lags da média OHLC (quantos dias passados usar)
    ohlc_lags: 10  # Usar 10 dias passados da média OHLC
    pct_threshold: 0.5  # Threshold para considerar pct_change como relevante (0.5%)

    # Lags das features econométricas (quantos dias passados usar)
    econometric_lags: 5  # Usar 5 dias passados das features econométricas

    # Janelas para cálculos
    volatility_window: 20  # Janela para cálculo da volatilidade
    spread_multiplier: 0.5  # Multiplicador para estimar spread baseado na volatilidade

  # Parâmetros do modelo XGBoost
  model_params:
    n_estimators: 100      # Número de árvores
    max_depth: 6           # Profundidade máxima das árvores
    learning_rate: 0.1     # Taxa de aprendizado
    random_state: 42       # Seed para reprodutibilidade
    eval_metric: 'logloss' # Métrica de avaliação

  # Divisão dos dados
  test_size: 0.2  # 20% para teste

  # Divisão temporal dos dados
  temporal_split:
    use_temporal_split: true  # Usar divisão temporal ao invés de aleatória

  # Normalização
  use_standard_scaler: true  # Usar StandardScaler para normalizar features

  # Threshold de probabilidade para sinais
  probability_threshold: 0.50  # Sinais só são gerados se probabilidade > 0.55

  # Configurações específicas para cálculos
  calculations:
    emv_volume_divisor: 1000000  # Divisor para volume no cálculo EMV (Ease of Movement)

  estimator:
    use_weighted_training: false  # Usar pesos no treinamento para amenizar impacto de grandes variações

# ============================================================================
# CONFIGURAÇÕES DO LSTM
# ============================================================================
lstm:
  # Parâmetros da arquitetura
  units: 50  # Número de unidades LSTM
  dropout: 0.2  # Taxa de dropout
  recurrent_dropout: 0.0  # Taxa de dropout recorrente
  dense_units: 25  # Unidades da camada densa

  # Configurações de bias
  use_bias: true  # Usar bias na camada LSTM
  dense_use_bias: true  # Usar bias na camada densa
  output_use_bias: true  # Usar bias na camada de saída

  # Inicializadores de pesos
  kernel_initializer: 'glorot_uniform'  # Inicializador dos pesos da camada LSTM
  recurrent_initializer: 'orthogonal'  # Inicializador dos pesos recorrentes
  bias_initializer: 'zeros'  # Inicializador do bias da camada LSTM
  dense_kernel_initializer: 'glorot_uniform'  # Inicializador dos pesos da camada densa
  dense_bias_initializer: 'zeros'  # Inicializador do bias da camada densa
  output_kernel_initializer: 'glorot_uniform'  # Inicializador dos pesos da saída
  output_bias_initializer: 'zeros'  # Inicializador do bias da saída

  # Normalização
  normalize_target: true  # Normalizar o target (variação percentual) com StandardScaler

  # Parâmetros de treinamento
  epochs: 50  # Número de épocas
  batch_size: 32  # Tamanho do batch
  patience: 15  # Paciência para early stopping
  optimizer: 'adam'  # Otimizador (adam, sgd, rmsprop)
  learning_rate: 0.001  # Taxa de aprendizado

  # Parâmetros de sequência
  sequence_length: 60  # Comprimento da sequência (janela de dias)
# ============================================================================
# CONFIGURAÇÕES DE SIMULAÇÃO E ANÁLISE DE CARTEIRA
# ============================================================================
simulation:

  # Configurações de trading
  trading:
    # Quantidade fixa de ações para comprar a cada sinal
    fixed_quantity_per_stock: 50  # Comprar 100 ações por vez (se capital permitir)

  # Configurações específicas para comparação de estratégias
  comparison:
    # Capital inicial para comparação de estratégias (pode ser diferente do capital da carteira)
    initial_capital: 1000.0  # R$ 1000 para comparação
    simulation_period: '1y'  # 1 ano para comparação

    # Configurações do método bootstrap
    bootstrap:
      num_scenarios: 10  # Número de cenários bootstrap
      stocks_per_scenario: 31  # Número de ações por cenário
      random_seed: 42  # Seed base para reprodutibilidade

  # Configurações para análise de carteira
  portfolio_analysis:
    # Capital inicial para análise de carteira
    initial_capital: 1000.0  # R$ 1000 para análise de carteira

# ============================================================================
# CONFIGURAÇÕES DE PERFORMANCE
# ============================================================================
performance:
  # Configurações de memória para TensorFlow
  tensorflow_memory_growth: true

  # Configurações de processamento paralelo
  parallel_processing: false  # Desabilitado por padrão
  max_workers: 4  # Número máximo de workers para processamento paralelo
