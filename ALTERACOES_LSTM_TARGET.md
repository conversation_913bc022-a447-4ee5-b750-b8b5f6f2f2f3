# Alterações no Estimador LSTM - Target para Média OHLC

## 📋 Resumo das Alterações

O estimador LSTM foi modificado para predizer o **valor da média OHLC** ao invés da **variação percentual**. As alterações mantêm a compatibilidade com o pós-processamento, onde a variação percentual é calculada para gerar os sinais de compra/venda.

## 🔄 Principais Mudanças

### 1. **Target do Modelo**
- **Antes**: `Target_PctChange` (variação percentual)
- **Depois**: `Target_Media_OHLC` (valor da média OHLC do dia seguinte)

```python
# ANTES
dataset['Target_PctChange'] = (
    (dataset['Media_OHLC'] - dataset['Media_OHLC_Anterior']) /
    dataset['Media_OHLC_Anterior'] * 100
)

# DEPOIS
dataset['Target_Media_OHLC'] = dataset.groupby('Ticker')['Media_OHLC'].shift(-1)
```

### 2. **Função preparar_dados_lstm()**
- Atualizada documentação para refletir que prediz valores da média OHLC
- Target agora é o valor absoluto ao invés de variação percentual

### 3. **Função aplicar_predicoes_lstm()**
- Modelo agora prediz diretamente o valor da média OHLC
- Variação percentual é calculada no pós-processamento:
```python
# Predição direta do valor
preco_previsto = model.predict(X_scaled)[0][0]

# Cálculo da variação percentual para sinais
variacao_pct_prevista = ((preco_previsto - preco_atual) / preco_atual) * 100
```

### 4. **Gráficos e Visualizações**
- Todos os gráficos atualizados para mostrar valores em R$ ao invés de %
- Títulos e labels dos eixos alterados para "Média OHLC" ao invés de "Variação"
- Métricas (RMSE, MAE) agora em reais ao invés de pontos percentuais

### 5. **Mensagens de Saída**
- Recomendações mostram "Média OHLC atual/prevista" ao invés de "Preço atual/previsto"
- Mensagens indicam que o LSTM "prevê alta/queda na média OHLC"
- Métricas salvas em CSV com unidade "reais" ao invés de "pontos_percentuais"

## 🎯 Benefícios das Alterações

1. **Predição Mais Direta**: O modelo aprende diretamente os valores de preço
2. **Melhor Interpretabilidade**: Valores absolutos são mais fáceis de interpretar
3. **Compatibilidade Mantida**: Sinais ainda baseados em variação percentual
4. **Métricas Claras**: RMSE e MAE em reais são mais intuitivos

## 📊 Impacto nos Resultados

- **Gráficos**: Mostram valores da média OHLC em R$ ao invés de variações em %
- **Sinais**: Continuam baseados no threshold de variação percentual (configurável)
- **Métricas**: RMSE e MAE agora em reais, facilitando interpretação
- **CSV**: Arquivos de saída mantêm compatibilidade com análises existentes

## ✅ Testes Realizados

Os testes confirmaram que:
1. Target é calculado corretamente (valor da média OHLC do dia seguinte)
2. Variação percentual é calculada corretamente no pós-processamento
3. Sinais de compra/venda mantêm a lógica original

## 📊 Alterações Adicionais nos Gráficos (Para Comparação com XGBoost)

### 6. **Gráficos em Função da Variação Percentual**
Para permitir comparação direta com o XGBoost, todos os gráficos foram modificados para mostrar análises em função da **variação percentual**:

```python
# Converter valores absolutos para variações percentuais
y_test_pct = ((y_test - y_test_anterior) / y_test_anterior) * 100
y_pred_pct = ((y_pred - y_test_anterior) / y_test_anterior) * 100

# Calcular métricas em termos de variação percentual
rmse_pct = np.sqrt(np.mean((y_pred_pct - y_test_pct)**2))
mae_pct = np.mean(np.abs(y_pred_pct - y_test_pct))
```

### 7. **Função preparar_dados_lstm() Atualizada**
- Agora retorna também os valores anteriores: `X, y, y_anterior`
- Permite calcular variações percentuais nos gráficos
- Mantém compatibilidade com o treinamento do modelo

### 8. **Gráficos Comparáveis**
Todos os 6 gráficos agora mostram:
- **Eixos em %**: Variações percentuais ao invés de valores absolutos
- **Métricas em %**: RMSE e MAE em pontos percentuais
- **Análises por faixa**: Baseadas em variação percentual
- **Compatibilidade**: Direta com gráficos do XGBoost

## 🎯 Benefícios das Alterações nos Gráficos

1. **Comparação Direta**: Gráficos LSTM e XGBoost agora são diretamente comparáveis
2. **Métricas Padronizadas**: RMSE e MAE em pontos percentuais para ambos os modelos
3. **Análise Unificada**: Mesma escala e interpretação para ambos os modelos
4. **Flexibilidade**: Modelo treina com valores absolutos, análise em variação percentual

## 🚀 Próximos Passos

Para testar as alterações:
```bash
uv run python src/estimador_lstm_sinais.py
```

O modelo agora:
- Treina para predizer valores absolutos da média OHLC
- Gera gráficos com **variações percentuais** (comparáveis ao XGBoost)
- Calcula variações percentuais para sinais de trading
- Mantém compatibilidade com o pipeline existente
- Permite análise comparativa direta entre LSTM e XGBoost
