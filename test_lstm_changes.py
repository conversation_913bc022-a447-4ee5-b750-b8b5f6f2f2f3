#!/usr/bin/env python3
"""
Script de teste para verificar as alterações no estimador LSTM
Testa se o target foi alterado corretamente para usar a média OHLC
"""

import sys
import os
import pandas as pd
import numpy as np

# Adicionar o diretório src ao path
sys.path.append('src')

def test_target_calculation():
    """
    Testa se o cálculo do target está correto
    """
    print("🧪 Testando cálculo do target...")
    
    # Criar dados de exemplo
    dates = pd.date_range('2024-01-01', periods=10, freq='D')
    test_data = pd.DataFrame({
        'Date': dates,
        'Ticker': ['PETR4.SA'] * 10,
        'Media_OHLC': [10.0, 10.5, 11.0, 10.8, 11.2, 11.5, 11.1, 11.8, 12.0, 11.9]
    })
    
    # Aplicar a lógica do target
    test_data = test_data.sort_values(['Ticker', 'Date'])
    test_data['Target_Media_OHLC'] = test_data.groupby('Ticker')['Media_OHLC'].shift(-1)
    
    print("📊 Dados de teste:")
    print(test_data[['Date', 'Media_OHLC', 'Target_Media_OHLC']].head(8))
    
    # Verificar se o target está correto
    expected_targets = [10.5, 11.0, 10.8, 11.2, 11.5, 11.1, 11.8, 12.0, 11.9]
    actual_targets = test_data['Target_Media_OHLC'].dropna().tolist()
    
    if actual_targets == expected_targets:
        print("✅ Target calculado corretamente!")
        print(f"   • Target é o valor da média OHLC do dia seguinte")
        print(f"   • Exemplo: Para dia 1 (OHLC=10.0), target=10.5 (OHLC do dia 2)")
    else:
        print("❌ Erro no cálculo do target!")
        print(f"   • Esperado: {expected_targets}")
        print(f"   • Obtido: {actual_targets}")
    
    return actual_targets == expected_targets

def test_variacao_calculation():
    """
    Testa se o cálculo da variação percentual no pós-processamento está correto
    """
    print("\n🧪 Testando cálculo da variação percentual...")
    
    # Simular predição
    preco_atual = 10.0
    preco_previsto = 10.5
    
    # Calcular variação percentual (como no código alterado)
    variacao_pct = ((preco_previsto - preco_atual) / preco_atual) * 100
    
    expected_variacao = 5.0  # 5% de alta
    
    if abs(variacao_pct - expected_variacao) < 0.001:
        print("✅ Variação percentual calculada corretamente!")
        print(f"   • Preço atual: R$ {preco_atual:.2f}")
        print(f"   • Preço previsto: R$ {preco_previsto:.2f}")
        print(f"   • Variação: {variacao_pct:+.2f}%")
    else:
        print("❌ Erro no cálculo da variação percentual!")
        print(f"   • Esperado: {expected_variacao}%")
        print(f"   • Obtido: {variacao_pct}%")
    
    return abs(variacao_pct - expected_variacao) < 0.001

def main():
    """
    Executa todos os testes
    """
    print("🔬 TESTE DAS ALTERAÇÕES NO ESTIMADOR LSTM")
    print("=" * 50)
    print("📝 Verificando se o target foi alterado corretamente")
    print("   • Antes: Target_PctChange (variação percentual)")
    print("   • Depois: Target_Media_OHLC (valor da média OHLC)")
    print("=" * 50)
    
    # Executar testes
    test1_passed = test_target_calculation()
    test2_passed = test_variacao_calculation()
    
    # Resumo
    print("\n📋 RESUMO DOS TESTES:")
    print(f"   • Cálculo do target: {'✅ PASSOU' if test1_passed else '❌ FALHOU'}")
    print(f"   • Cálculo da variação: {'✅ PASSOU' if test2_passed else '❌ FALHOU'}")
    
    if test1_passed and test2_passed:
        print("\n🎉 TODOS OS TESTES PASSARAM!")
        print("   • O estimador LSTM agora prediz valores da média OHLC")
        print("   • A variação percentual é calculada no pós-processamento")
        print("   • Os gráficos e resultados mostrarão variações em relação ao valor")
    else:
        print("\n⚠️ ALGUNS TESTES FALHARAM!")
        print("   • Verifique as alterações no código")

if __name__ == "__main__":
    main()
