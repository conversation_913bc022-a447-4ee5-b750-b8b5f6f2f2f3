#!/usr/bin/env python3
"""
Demonstração da comparação entre gráficos LSTM e XGBoost
Mostra como os gráficos agora são comparáveis
"""

import numpy as np
import matplotlib.pyplot as plt
import pandas as pd
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score

def simular_dados_comparacao():
    """
    Simula dados para demonstrar a comparação entre LSTM e XGBoost
    """
    np.random.seed(42)
    n_samples = 1000
    
    # Simular variações percentuais reais
    y_real_pct = np.random.normal(0, 2, n_samples)  # Média 0%, desvio 2%
    
    # Simular predições LSTM (convertidas para variação percentual)
    y_lstm_pct = y_real_pct + np.random.normal(0, 0.8, n_samples)  # Erro menor
    
    # Simular predições XGBoost (já em variação percentual)
    y_xgb_pct = y_real_pct + np.random.normal(0, 1.2, n_samples)  # Erro maior
    
    return y_real_pct, y_lstm_pct, y_xgb_pct

def calcular_metricas(y_real, y_pred, modelo_nome):
    """
    Calcula métricas para um modelo
    """
    rmse = np.sqrt(mean_squared_error(y_real, y_pred))
    mae = mean_absolute_error(y_real, y_pred)
    r2 = r2_score(y_real, y_pred)
    
    print(f"\n📊 Métricas {modelo_nome}:")
    print(f"   • RMSE: {rmse:.2f}%")
    print(f"   • MAE: {mae:.2f}%")
    print(f"   • R²: {r2:.4f}")
    
    return rmse, mae, r2

def criar_grafico_comparacao(y_real, y_lstm, y_xgb):
    """
    Cria gráfico de comparação entre LSTM e XGBoost
    """
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 12))
    
    # 1. Scatter plot LSTM
    ax1.scatter(y_real, y_lstm, alpha=0.6, color='steelblue', s=20)
    min_val = min(y_real.min(), y_lstm.min())
    max_val = max(y_real.max(), y_lstm.max())
    ax1.plot([min_val, max_val], [min_val, max_val], 'r--', linewidth=2, label='Predição Perfeita')
    ax1.set_xlabel('Variação Real (%)')
    ax1.set_ylabel('Variação Predita (%)')
    ax1.set_title('LSTM: Variações Reais vs Preditas')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # 2. Scatter plot XGBoost
    ax2.scatter(y_real, y_xgb, alpha=0.6, color='orange', s=20)
    ax2.plot([min_val, max_val], [min_val, max_val], 'r--', linewidth=2, label='Predição Perfeita')
    ax2.set_xlabel('Variação Real (%)')
    ax2.set_ylabel('Variação Predita (%)')
    ax2.set_title('XGBoost: Variações Reais vs Preditas')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    # 3. Distribuição dos erros LSTM
    erros_lstm = y_lstm - y_real
    ax3.hist(erros_lstm, bins=50, alpha=0.7, color='steelblue', edgecolor='black')
    ax3.axvline(0, color='red', linestyle='--', linewidth=2, label='Erro Zero')
    ax3.set_xlabel('Erro (pontos percentuais)')
    ax3.set_ylabel('Frequência')
    ax3.set_title(f'LSTM: Distribuição dos Erros\nMédia: {erros_lstm.mean():.3f}pp')
    ax3.legend()
    ax3.grid(True, alpha=0.3)
    
    # 4. Distribuição dos erros XGBoost
    erros_xgb = y_xgb - y_real
    ax4.hist(erros_xgb, bins=50, alpha=0.7, color='orange', edgecolor='black')
    ax4.axvline(0, color='red', linestyle='--', linewidth=2, label='Erro Zero')
    ax4.set_xlabel('Erro (pontos percentuais)')
    ax4.set_ylabel('Frequência')
    ax4.set_title(f'XGBoost: Distribuição dos Erros\nMédia: {erros_xgb.mean():.3f}pp')
    ax4.legend()
    ax4.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('demo_comparacao_lstm_xgboost.png', dpi=300, bbox_inches='tight')
    plt.show()

def criar_tabela_comparacao(y_real, y_lstm, y_xgb):
    """
    Cria tabela de comparação das métricas
    """
    # Calcular métricas
    rmse_lstm, mae_lstm, r2_lstm = calcular_metricas(y_real, y_lstm, "LSTM")
    rmse_xgb, mae_xgb, r2_xgb = calcular_metricas(y_real, y_xgb, "XGBoost")
    
    # Criar DataFrame de comparação
    comparacao = pd.DataFrame({
        'Métrica': ['RMSE (%)', 'MAE (%)', 'R²'],
        'LSTM': [f'{rmse_lstm:.2f}', f'{mae_lstm:.2f}', f'{r2_lstm:.4f}'],
        'XGBoost': [f'{rmse_xgb:.2f}', f'{mae_xgb:.2f}', f'{r2_xgb:.4f}'],
        'Melhor': [
            'LSTM' if rmse_lstm < rmse_xgb else 'XGBoost',
            'LSTM' if mae_lstm < mae_xgb else 'XGBoost',
            'LSTM' if r2_lstm > r2_xgb else 'XGBoost'
        ]
    })
    
    print("\n📋 TABELA DE COMPARAÇÃO:")
    print("=" * 50)
    print(comparacao.to_string(index=False))
    print("=" * 50)
    
    return comparacao

def main():
    """
    Executa a demonstração completa
    """
    print("🎯 DEMONSTRAÇÃO: COMPARAÇÃO LSTM vs XGBoost")
    print("=" * 60)
    print("📊 Gráficos agora são diretamente comparáveis")
    print("   • Ambos mostram variações percentuais")
    print("   • Métricas em pontos percentuais")
    print("   • Mesma escala e interpretação")
    print("=" * 60)
    
    # Simular dados
    print("\n🔬 Simulando dados de exemplo...")
    y_real, y_lstm, y_xgb = simular_dados_comparacao()
    
    print(f"   • {len(y_real)} amostras simuladas")
    print(f"   • Variação real: {y_real.mean():.2f}% ± {y_real.std():.2f}%")
    print(f"   • LSTM: erro simulado menor (modelo mais preciso)")
    print(f"   • XGBoost: erro simulado maior (modelo menos preciso)")
    
    # Calcular e comparar métricas
    print("\n📊 Calculando métricas...")
    tabela = criar_tabela_comparacao(y_real, y_lstm, y_xgb)
    
    # Criar gráficos de comparação
    print("\n📈 Criando gráficos de comparação...")
    criar_grafico_comparacao(y_real, y_lstm, y_xgb)
    
    # Resumo
    print("\n🎉 DEMONSTRAÇÃO CONCLUÍDA!")
    print("   ✅ Gráficos LSTM e XGBoost agora são comparáveis")
    print("   ✅ Métricas padronizadas em pontos percentuais")
    print("   ✅ Análise unificada para ambos os modelos")
    print("   📁 Gráfico salvo: demo_comparacao_lstm_xgboost.png")
    
    print("\n🔍 INTERPRETAÇÃO:")
    print("   • Scatter plots mostram qualidade das predições")
    print("   • Histogramas mostram distribuição dos erros")
    print("   • Tabela permite comparação direta das métricas")
    print("   • Escala unificada facilita análise comparativa")

if __name__ == "__main__":
    main()
