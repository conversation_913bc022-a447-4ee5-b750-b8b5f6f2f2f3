#!/usr/bin/env python3
"""
Script de teste para verificar se a saturação do LSTM está funcionando
"""

import sys
import os
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt

# Adicionar o diretório src ao path
sys.path.append('src')

def test_saturacao_tanh():
    """
    Testa a função de saturação tanh
    """
    print("🧪 Testando saturação tanh...")
    
    # Simular valores de entrada (antes da saturação)
    x = np.linspace(-3, 3, 100)
    
    # Aplicar saturação tanh com diferentes fatores
    factor_05 = 0.5
    factor_10 = 1.0
    factor_20 = 2.0
    
    y_tanh_05 = np.tanh(x) * factor_05
    y_tanh_10 = np.tanh(x) * factor_10
    y_tanh_20 = np.tanh(x) * factor_20
    y_linear = x  # Sem saturação
    
    # Criar gráfico
    plt.figure(figsize=(12, 8))
    plt.plot(x, y_linear, 'k--', label='Linear (sem saturação)', linewidth=2)
    plt.plot(x, y_tanh_05, 'r-', label=f'Tanh × {factor_05}', linewidth=2)
    plt.plot(x, y_tanh_10, 'g-', label=f'Tanh × {factor_10}', linewidth=2)
    plt.plot(x, y_tanh_20, 'b-', label=f'Tanh × {factor_20}', linewidth=2)
    
    plt.xlabel('Entrada')
    plt.ylabel('Saída')
    plt.title('Comparação de Funções de Saturação Tanh')
    plt.legend()
    plt.grid(True, alpha=0.3)
    plt.axhline(y=0, color='k', linestyle='-', alpha=0.3)
    plt.axvline(x=0, color='k', linestyle='-', alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('test_saturacao_tanh.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print(f"📊 Características da saturação tanh:")
    print(f"   • Factor 0.5: saída limitada a [-0.5, 0.5]")
    print(f"   • Factor 1.0: saída limitada a [-1.0, 1.0]")
    print(f"   • Factor 2.0: saída limitada a [-2.0, 2.0]")
    print(f"   • Reduz viés em valores intermediários")
    print(f"📁 Gráfico salvo: test_saturacao_tanh.png")

def test_saturacao_sigmoid():
    """
    Testa a função de saturação sigmoid
    """
    print("\n🧪 Testando saturação sigmoid...")
    
    # Simular valores de entrada
    x = np.linspace(-3, 3, 100)
    
    # Aplicar saturação sigmoid com diferentes fatores
    factor_05 = 0.5
    factor_10 = 1.0
    factor_20 = 2.0
    
    # Sigmoid escalada e centralizada: (sigmoid - 0.5) * 2 * factor
    y_sigmoid_05 = (1 / (1 + np.exp(-x)) - 0.5) * 2 * factor_05
    y_sigmoid_10 = (1 / (1 + np.exp(-x)) - 0.5) * 2 * factor_10
    y_sigmoid_20 = (1 / (1 + np.exp(-x)) - 0.5) * 2 * factor_20
    y_linear = x  # Sem saturação
    
    # Criar gráfico
    plt.figure(figsize=(12, 8))
    plt.plot(x, y_linear, 'k--', label='Linear (sem saturação)', linewidth=2)
    plt.plot(x, y_sigmoid_05, 'r-', label=f'Sigmoid × {factor_05}', linewidth=2)
    plt.plot(x, y_sigmoid_10, 'g-', label=f'Sigmoid × {factor_10}', linewidth=2)
    plt.plot(x, y_sigmoid_20, 'b-', label=f'Sigmoid × {factor_20}', linewidth=2)
    
    plt.xlabel('Entrada')
    plt.ylabel('Saída')
    plt.title('Comparação de Funções de Saturação Sigmoid')
    plt.legend()
    plt.grid(True, alpha=0.3)
    plt.axhline(y=0, color='k', linestyle='-', alpha=0.3)
    plt.axvline(x=0, color='k', linestyle='-', alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('test_saturacao_sigmoid.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print(f"📊 Características da saturação sigmoid:")
    print(f"   • Factor 0.5: saída limitada a [-0.5, 0.5]")
    print(f"   • Factor 1.0: saída limitada a [-1.0, 1.0]")
    print(f"   • Factor 2.0: saída limitada a [-2.0, 2.0]")
    print(f"   • Transição mais suave que tanh")
    print(f"📁 Gráfico salvo: test_saturacao_sigmoid.png")

def test_saturacao_clip():
    """
    Testa a função de saturação por clipping
    """
    print("\n🧪 Testando saturação por clipping...")
    
    # Simular valores de entrada
    x = np.linspace(-3, 3, 100)
    
    # Aplicar saturação por clipping com diferentes fatores
    factor_05 = 0.5
    factor_10 = 1.0
    factor_20 = 2.0
    
    y_clip_05 = np.clip(x, -factor_05, factor_05)
    y_clip_10 = np.clip(x, -factor_10, factor_10)
    y_clip_20 = np.clip(x, -factor_20, factor_20)
    y_linear = x  # Sem saturação
    
    # Criar gráfico
    plt.figure(figsize=(12, 8))
    plt.plot(x, y_linear, 'k--', label='Linear (sem saturação)', linewidth=2)
    plt.plot(x, y_clip_05, 'r-', label=f'Clip ±{factor_05}', linewidth=2)
    plt.plot(x, y_clip_10, 'g-', label=f'Clip ±{factor_10}', linewidth=2)
    plt.plot(x, y_clip_20, 'b-', label=f'Clip ±{factor_20}', linewidth=2)
    
    plt.xlabel('Entrada')
    plt.ylabel('Saída')
    plt.title('Comparação de Funções de Saturação por Clipping')
    plt.legend()
    plt.grid(True, alpha=0.3)
    plt.axhline(y=0, color='k', linestyle='-', alpha=0.3)
    plt.axvline(x=0, color='k', linestyle='-', alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('test_saturacao_clip.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print(f"📊 Características da saturação por clipping:")
    print(f"   • Factor 0.5: saída limitada a [-0.5, 0.5]")
    print(f"   • Factor 1.0: saída limitada a [-1.0, 1.0]")
    print(f"   • Factor 2.0: saída limitada a [-2.0, 2.0]")
    print(f"   • Transição abrupta (não diferenciável nos limites)")
    print(f"📁 Gráfico salvo: test_saturacao_clip.png")

def simular_efeito_no_viés():
    """
    Simula o efeito da saturação no viés de valores intermediários
    """
    print("\n🧪 Simulando efeito no viés...")
    
    # Simular dados com viés (problema comum em LSTM)
    np.random.seed(42)
    n_samples = 1000
    
    # Valores reais (distribuição normal centrada em 0)
    y_real = np.random.normal(0, 1, n_samples)
    
    # Predições com viés (LSTM tende a predizer valores próximos à média)
    y_pred_biased = y_real * 0.3 + np.random.normal(0, 0.2, n_samples)  # Viés: reduz amplitude
    
    # Predições com saturação tanh (corrige parcialmente o viés)
    y_pred_saturated = np.tanh(y_real + np.random.normal(0, 0.3, n_samples)) * 1.5
    
    # Criar gráfico comparativo
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 12))
    
    # Scatter plot sem saturação
    ax1.scatter(y_real, y_pred_biased, alpha=0.6, s=20)
    ax1.plot([-3, 3], [-3, 3], 'r--', linewidth=2, label='Predição Perfeita')
    ax1.set_xlabel('Valores Reais')
    ax1.set_ylabel('Predições (com viés)')
    ax1.set_title('LSTM sem Saturação (com viés)')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # Scatter plot com saturação
    ax2.scatter(y_real, y_pred_saturated, alpha=0.6, s=20, color='orange')
    ax2.plot([-3, 3], [-3, 3], 'r--', linewidth=2, label='Predição Perfeita')
    ax2.set_xlabel('Valores Reais')
    ax2.set_ylabel('Predições (com saturação)')
    ax2.set_title('LSTM com Saturação Tanh')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    # Histograma dos erros sem saturação
    erros_biased = y_pred_biased - y_real
    ax3.hist(erros_biased, bins=50, alpha=0.7, color='skyblue', edgecolor='black')
    ax3.axvline(erros_biased.mean(), color='red', linestyle='--', linewidth=2, 
                label=f'Média: {erros_biased.mean():.3f}')
    ax3.set_xlabel('Erro')
    ax3.set_ylabel('Frequência')
    ax3.set_title('Distribuição dos Erros (sem saturação)')
    ax3.legend()
    ax3.grid(True, alpha=0.3)
    
    # Histograma dos erros com saturação
    erros_saturated = y_pred_saturated - y_real
    ax4.hist(erros_saturated, bins=50, alpha=0.7, color='orange', edgecolor='black')
    ax4.axvline(erros_saturated.mean(), color='red', linestyle='--', linewidth=2, 
                label=f'Média: {erros_saturated.mean():.3f}')
    ax4.set_xlabel('Erro')
    ax4.set_ylabel('Frequência')
    ax4.set_title('Distribuição dos Erros (com saturação)')
    ax4.legend()
    ax4.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('test_efeito_saturacao_vies.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print(f"📊 Comparação de viés:")
    print(f"   • Sem saturação - Erro médio: {erros_biased.mean():.3f}")
    print(f"   • Com saturação - Erro médio: {erros_saturated.mean():.3f}")
    print(f"   • Saturação pode ajudar a reduzir viés em valores intermediários")
    print(f"📁 Gráfico salvo: test_efeito_saturacao_vies.png")

def main():
    """
    Executa todos os testes de saturação
    """
    print("🔬 TESTE DAS FUNÇÕES DE SATURAÇÃO DO LSTM")
    print("=" * 60)
    print("🎯 Objetivo: Corrigir viés em valores intermediários")
    print("🎯 Métodos: tanh, sigmoid, clipping")
    print("=" * 60)
    
    # Testar diferentes tipos de saturação
    test_saturacao_tanh()
    test_saturacao_sigmoid()
    test_saturacao_clip()
    
    # Simular efeito no viés
    simular_efeito_no_viés()
    
    print("\n🎉 TESTES CONCLUÍDOS!")
    print("   📊 3 tipos de saturação testados")
    print("   📈 4 gráficos gerados")
    print("   🎯 Saturação pode ajudar a corrigir viés do LSTM")
    
    print("\n💡 RECOMENDAÇÕES:")
    print("   • Tanh: Boa para valores simétricos, transição suave")
    print("   • Sigmoid: Similar ao tanh, mas com forma ligeiramente diferente")
    print("   • Clipping: Mais agressivo, pode causar gradientes zero")
    print("   • Factor 0.5-1.0: Bom ponto de partida para testes")

if __name__ == "__main__":
    main()
